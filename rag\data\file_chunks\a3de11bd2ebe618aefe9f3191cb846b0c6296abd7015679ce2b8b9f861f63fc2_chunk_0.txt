漏洞：CVE-2011-2856
在 14.0.835.163 之前的 Google Chrome 中使用的 Google V8 允许远程攻击者通过不明向量绕过同源策略。
CVE-2011-3243漏洞
WebKit 中的跨站点脚本 （XSS） 漏洞（在低于 5.1.1 的 Apple iOS 和 Safari 5.1.1 之前版本中使用）允许远程攻击者通过涉及非活动 DOM 窗口的向量注入任意 Web 脚本或 HTML。
漏洞：CVE-2013-2618
0.97b 之前的网络天气图的 editor.php 中存在跨站脚本 （XSS） 漏洞，允许远程攻击者通过 map_title 参数注入任意 Web 脚本或 HTML。
CVE-2013-6632漏洞
低于 31.0.1650.57 的 Google Chrome 中存在整数溢出漏洞，允许远程攻击者通过不明向量执行任意代码或造成拒绝服务（内存损坏），如 PacSec 2013 的 Mobile Pwn2Own 竞赛中所示。
CVE-2014-1701漏洞