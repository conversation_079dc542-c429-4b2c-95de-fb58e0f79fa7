2025/05/26-21:15:56.990623 167cc RocksDB version: 6.26.1
2025/05/26-21:15:56.990686 167cc Git sha 0
2025/05/26-21:15:56.990707 167cc Compile date 2021-12-13 18:23:52
2025/05/26-21:15:56.990725 167cc DB SUMMARY
2025/05/26-21:15:56.990762 167cc DB Session ID:  GO9J0WL86KLYROV1ROO3
2025/05/26-21:15:56.990823 167cc SST files in E:\CExperiment\ThreatRAG\test_milvus_lite\data\rocketmq dir, Total Num: 0, files: 
2025/05/26-21:15:56.990842 167cc Write Ahead Log file in E:\CExperiment\ThreatRAG\test_milvus_lite\data\rocketmq: 
2025/05/26-21:15:56.990859 167cc                         Options.error_if_exists: 0
2025/05/26-21:15:56.990874 167cc                       Options.create_if_missing: 1
2025/05/26-21:15:56.990891 167cc                         Options.paranoid_checks: 1
2025/05/26-21:15:56.991453 167cc             Options.flush_verify_memtable_count: 1
2025/05/26-21:15:56.991463 167cc                               Options.track_and_verify_wals_in_manifest: 0
2025/05/26-21:15:56.991471 167cc                                     Options.env: 0000019a6d733ab0
2025/05/26-21:15:56.991479 167cc                                      Options.fs: WinFS
2025/05/26-21:15:56.991486 167cc                                Options.info_log: 0000019a6d7c6500
2025/05/26-21:15:56.991493 167cc                Options.max_file_opening_threads: 16
2025/05/26-21:15:56.991501 167cc                              Options.statistics: 0000000000000000
2025/05/26-21:15:56.991508 167cc                               Options.use_fsync: 0
2025/05/26-21:15:56.991515 167cc                       Options.max_log_file_size: 0
2025/05/26-21:15:56.991522 167cc                  Options.max_manifest_file_size: 1073741824
2025/05/26-21:15:56.991529 167cc                   Options.log_file_time_to_roll: 0
2025/05/26-21:15:56.991535 167cc                       Options.keep_log_file_num: 1000
2025/05/26-21:15:56.991542 167cc                    Options.recycle_log_file_num: 0
2025/05/26-21:15:56.991549 167cc                         Options.allow_fallocate: 1
2025/05/26-21:15:56.991556 167cc                        Options.allow_mmap_reads: 0
2025/05/26-21:15:56.991563 167cc                       Options.allow_mmap_writes: 0
2025/05/26-21:15:56.991569 167cc                        Options.use_direct_reads: 0
2025/05/26-21:15:56.991576 167cc                        Options.use_direct_io_for_flush_and_compaction: 0
2025/05/26-21:15:56.991583 167cc          Options.create_missing_column_families: 0
2025/05/26-21:15:56.991603 167cc                              Options.db_log_dir: 
2025/05/26-21:15:56.991610 167cc                                 Options.wal_dir: 
2025/05/26-21:15:56.991617 167cc                Options.table_cache_numshardbits: 6
2025/05/26-21:15:56.991624 167cc                         Options.WAL_ttl_seconds: 0
2025/05/26-21:15:56.991631 167cc                       Options.WAL_size_limit_MB: 0
2025/05/26-21:15:56.991638 167cc                        Options.max_write_batch_group_size_bytes: 1048576
2025/05/26-21:15:56.991645 167cc             Options.manifest_preallocation_size: 4194304
2025/05/26-21:15:56.991652 167cc                     Options.is_fd_close_on_exec: 1
2025/05/26-21:15:56.991661 167cc                   Options.advise_random_on_open: 1
2025/05/26-21:15:56.991667 167cc                   Options.experimental_mempurge_threshold: 0.000000
2025/05/26-21:15:56.991675 167cc                    Options.db_write_buffer_size: 0
2025/05/26-21:15:56.991682 167cc                    Options.write_buffer_manager: 0000019a6d7a7520
2025/05/26-21:15:56.991719 167cc         Options.access_hint_on_compaction_start: 1
2025/05/26-21:15:56.991726 167cc  Options.new_table_reader_for_compaction_inputs: 0
2025/05/26-21:15:56.991733 167cc           Options.random_access_max_buffer_size: 1048576
2025/05/26-21:15:56.991739 167cc                      Options.use_adaptive_mutex: 0
2025/05/26-21:15:56.991746 167cc                            Options.rate_limiter: 0000000000000000
2025/05/26-21:15:56.991753 167cc     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/05/26-21:15:56.991760 167cc                       Options.wal_recovery_mode: 2
2025/05/26-21:15:56.991768 167cc                  Options.enable_thread_tracking: 0
2025/05/26-21:15:56.991776 167cc                  Options.enable_pipelined_write: 0
2025/05/26-21:15:56.991783 167cc                  Options.unordered_write: 0
2025/05/26-21:15:56.991790 167cc         Options.allow_concurrent_memtable_write: 1
2025/05/26-21:15:56.991797 167cc      Options.enable_write_thread_adaptive_yield: 1
2025/05/26-21:15:56.991804 167cc             Options.write_thread_max_yield_usec: 100
2025/05/26-21:15:56.991810 167cc            Options.write_thread_slow_yield_usec: 3
2025/05/26-21:15:56.991817 167cc                               Options.row_cache: None
2025/05/26-21:15:56.991823 167cc                              Options.wal_filter: None
2025/05/26-21:15:56.991831 167cc             Options.avoid_flush_during_recovery: 0
2025/05/26-21:15:56.991837 167cc             Options.allow_ingest_behind: 0
2025/05/26-21:15:56.991844 167cc             Options.preserve_deletes: 0
2025/05/26-21:15:56.991851 167cc             Options.two_write_queues: 0
2025/05/26-21:15:56.991857 167cc             Options.manual_wal_flush: 0
2025/05/26-21:15:56.991870 167cc             Options.atomic_flush: 0
2025/05/26-21:15:56.991877 167cc             Options.avoid_unnecessary_blocking_io: 0
2025/05/26-21:15:56.991883 167cc                 Options.persist_stats_to_disk: 0
2025/05/26-21:15:56.991890 167cc                 Options.write_dbid_to_manifest: 0
2025/05/26-21:15:56.991897 167cc                 Options.log_readahead_size: 0
2025/05/26-21:15:56.991904 167cc                 Options.file_checksum_gen_factory: Unknown
2025/05/26-21:15:56.991910 167cc                 Options.best_efforts_recovery: 0
2025/05/26-21:15:56.991917 167cc                Options.max_bgerror_resume_count: 2147483647
2025/05/26-21:15:56.991924 167cc            Options.bgerror_resume_retry_interval: 1000000
2025/05/26-21:15:56.991931 167cc             Options.allow_data_in_errors: 0
2025/05/26-21:15:56.991937 167cc             Options.db_host_id: __hostname__
2025/05/26-21:15:56.991944 167cc             Options.max_background_jobs: 2
2025/05/26-21:15:56.991951 167cc             Options.max_background_compactions: -1
2025/05/26-21:15:56.991957 167cc             Options.max_subcompactions: 1
2025/05/26-21:15:56.991964 167cc             Options.avoid_flush_during_shutdown: 0
2025/05/26-21:15:56.991971 167cc           Options.writable_file_max_buffer_size: 1048576
2025/05/26-21:15:56.991977 167cc             Options.delayed_write_rate : 16777216
2025/05/26-21:15:56.991984 167cc             Options.max_total_wal_size: 0
2025/05/26-21:15:56.991991 167cc             Options.delete_obsolete_files_period_micros: 21600000000
2025/05/26-21:15:56.991997 167cc                   Options.stats_dump_period_sec: 600
2025/05/26-21:15:56.992004 167cc                 Options.stats_persist_period_sec: 600
2025/05/26-21:15:56.992011 167cc                 Options.stats_history_buffer_size: 1048576
2025/05/26-21:15:56.992017 167cc                          Options.max_open_files: -1
2025/05/26-21:15:56.992024 167cc                          Options.bytes_per_sync: 0
2025/05/26-21:15:56.992030 167cc                      Options.wal_bytes_per_sync: 0
2025/05/26-21:15:56.992037 167cc                   Options.strict_bytes_per_sync: 0
2025/05/26-21:15:56.992044 167cc       Options.compaction_readahead_size: 0
2025/05/26-21:15:56.992050 167cc                  Options.max_background_flushes: 1
2025/05/26-21:15:56.992057 167cc Compression algorithms supported:
2025/05/26-21:15:56.992064 167cc 	kZSTD supported: 1
2025/05/26-21:15:56.992071 167cc 	kXpressCompression supported: 0
2025/05/26-21:15:56.992078 167cc 	kBZip2Compression supported: 1
2025/05/26-21:15:56.992085 167cc 	kZSTDNotFinalCompression supported: 1
2025/05/26-21:15:56.992098 167cc 	kLZ4Compression supported: 1
2025/05/26-21:15:56.992104 167cc 	kZlibCompression supported: 1
2025/05/26-21:15:56.992111 167cc 	kLZ4HCCompression supported: 1
2025/05/26-21:15:56.992117 167cc 	kSnappyCompression supported: 1
2025/05/26-21:15:56.992129 167cc Fast CRC32 supported: Supported on x86
2025/05/26-21:15:56.994220 167cc [db/db_impl/db_impl_open.cc:300] Creating manifest 1 
2025/05/26-21:15:56.996174 167cc [db/version_set.cc:4847] Recovering from manifest file: E:\CExperiment\ThreatRAG\test_milvus_lite\data\rocketmq/MANIFEST-000001
2025/05/26-21:15:56.996248 167cc [db/column_family.cc:609] --------------- Options for column family [default]:
2025/05/26-21:15:56.996260 167cc               Options.comparator: leveldb.BytewiseComparator
2025/05/26-21:15:56.996268 167cc           Options.merge_operator: None
2025/05/26-21:15:56.996276 167cc        Options.compaction_filter: None
2025/05/26-21:15:56.996283 167cc        Options.compaction_filter_factory: None
2025/05/26-21:15:56.996290 167cc  Options.sst_partitioner_factory: None
2025/05/26-21:15:56.996296 167cc         Options.memtable_factory: SkipListFactory
2025/05/26-21:15:56.996306 167cc            Options.table_factory: BlockBasedTable
2025/05/26-21:15:56.996345 167cc            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000019a6d7b0280)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0000019a6d753300
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 2036756152
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/05/26-21:15:56.996628 167cc        Options.write_buffer_size: 67108864
2025/05/26-21:15:56.996637 167cc  Options.max_write_buffer_number: 2
2025/05/26-21:15:56.996646 167cc        Options.compression[0]: NoCompression
2025/05/26-21:15:56.996654 167cc        Options.compression[1]: NoCompression
2025/05/26-21:15:56.996662 167cc        Options.compression[2]: ZSTD
2025/05/26-21:15:56.996669 167cc        Options.compression[3]: ZSTD
2025/05/26-21:15:56.996678 167cc        Options.compression[4]: ZSTD
2025/05/26-21:15:56.996685 167cc        Options.compression[5]: ZSTD
2025/05/26-21:15:56.996692 167cc        Options.compression[6]: ZSTD
2025/05/26-21:15:56.996699 167cc                  Options.bottommost_compression: Disabled
2025/05/26-21:15:56.996706 167cc       Options.prefix_extractor: nullptr
2025/05/26-21:15:56.996713 167cc   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/05/26-21:15:56.996720 167cc             Options.num_levels: 7
2025/05/26-21:15:56.996727 167cc        Options.min_write_buffer_number_to_merge: 1
2025/05/26-21:15:56.996734 167cc     Options.max_write_buffer_number_to_maintain: 0
2025/05/26-21:15:56.996740 167cc     Options.max_write_buffer_size_to_maintain: 0
2025/05/26-21:15:56.996747 167cc            Options.bottommost_compression_opts.window_bits: -14
2025/05/26-21:15:56.996754 167cc                  Options.bottommost_compression_opts.level: 32767
2025/05/26-21:15:56.996761 167cc               Options.bottommost_compression_opts.strategy: 0
2025/05/26-21:15:56.996767 167cc         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/05/26-21:15:56.996774 167cc         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/05/26-21:15:56.996782 167cc         Options.bottommost_compression_opts.parallel_threads: 1
2025/05/26-21:15:56.996789 167cc                  Options.bottommost_compression_opts.enabled: false
2025/05/26-21:15:56.996857 167cc         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/05/26-21:15:56.996866 167cc            Options.compression_opts.window_bits: -14
2025/05/26-21:15:56.996873 167cc                  Options.compression_opts.level: 32767
2025/05/26-21:15:56.996879 167cc               Options.compression_opts.strategy: 0
2025/05/26-21:15:56.996886 167cc         Options.compression_opts.max_dict_bytes: 0
2025/05/26-21:15:56.996893 167cc         Options.compression_opts.zstd_max_train_bytes: 0
2025/05/26-21:15:56.996900 167cc         Options.compression_opts.parallel_threads: 1
2025/05/26-21:15:56.996907 167cc                  Options.compression_opts.enabled: false
2025/05/26-21:15:56.996913 167cc         Options.compression_opts.max_dict_buffer_bytes: 0
2025/05/26-21:15:56.996929 167cc      Options.level0_file_num_compaction_trigger: 4
2025/05/26-21:15:56.996936 167cc          Options.level0_slowdown_writes_trigger: 20
2025/05/26-21:15:56.996942 167cc              Options.level0_stop_writes_trigger: 36
2025/05/26-21:15:56.996949 167cc                   Options.target_file_size_base: 67108864
2025/05/26-21:15:56.996956 167cc             Options.target_file_size_multiplier: 1
2025/05/26-21:15:56.996963 167cc                Options.max_bytes_for_level_base: 268435456
2025/05/26-21:15:56.996969 167cc Options.level_compaction_dynamic_level_bytes: 0
2025/05/26-21:15:56.996976 167cc          Options.max_bytes_for_level_multiplier: 10.000000
2025/05/26-21:15:56.996984 167cc Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/05/26-21:15:56.997060 167cc Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/05/26-21:15:56.997068 167cc Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/05/26-21:15:56.997076 167cc Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/05/26-21:15:56.997083 167cc Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/05/26-21:15:56.997090 167cc Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/05/26-21:15:56.997097 167cc Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/05/26-21:15:56.997103 167cc       Options.max_sequential_skip_in_iterations: 8
2025/05/26-21:15:56.997111 167cc                    Options.max_compaction_bytes: 1677721600
2025/05/26-21:15:56.997118 167cc                        Options.arena_block_size: 1048576
2025/05/26-21:15:56.997125 167cc   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/05/26-21:15:56.997132 167cc   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/05/26-21:15:56.997138 167cc       Options.rate_limit_delay_max_milliseconds: 100
2025/05/26-21:15:56.997145 167cc                Options.disable_auto_compactions: 0
2025/05/26-21:15:56.997153 167cc                        Options.compaction_style: kCompactionStyleLevel
2025/05/26-21:15:56.997160 167cc                          Options.compaction_pri: kMinOverlappingRatio
2025/05/26-21:15:56.997167 167cc Options.compaction_options_universal.size_ratio: 1
2025/05/26-21:15:56.997174 167cc Options.compaction_options_universal.min_merge_width: 2
2025/05/26-21:15:56.997181 167cc Options.compaction_options_universal.max_merge_width: 4294967295
2025/05/26-21:15:56.997187 167cc Options.compaction_options_universal.max_size_amplification_percent: 200
2025/05/26-21:15:56.997194 167cc Options.compaction_options_universal.compression_size_percent: -1
2025/05/26-21:15:56.997201 167cc Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/05/26-21:15:56.997208 167cc Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/05/26-21:15:56.997214 167cc Options.compaction_options_fifo.allow_compaction: 0
2025/05/26-21:15:56.997225 167cc                   Options.table_properties_collectors: 
2025/05/26-21:15:56.997239 167cc                   Options.inplace_update_support: 0
2025/05/26-21:15:56.997245 167cc                 Options.inplace_update_num_locks: 10000
2025/05/26-21:15:56.997252 167cc               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/05/26-21:15:56.997259 167cc               Options.memtable_whole_key_filtering: 0
2025/05/26-21:15:56.997268 167cc   Options.memtable_huge_page_size: 0
2025/05/26-21:15:56.997276 167cc                           Options.bloom_locality: 0
2025/05/26-21:15:56.997283 167cc                    Options.max_successive_merges: 0
2025/05/26-21:15:56.997289 167cc                Options.optimize_filters_for_hits: 0
2025/05/26-21:15:56.997296 167cc                Options.paranoid_file_checks: 0
2025/05/26-21:15:56.997303 167cc                Options.force_consistency_checks: 1
2025/05/26-21:15:56.997309 167cc                Options.report_bg_io_stats: 0
2025/05/26-21:15:56.997316 167cc                               Options.ttl: 2592000
2025/05/26-21:15:56.997323 167cc          Options.periodic_compaction_seconds: 0
2025/05/26-21:15:56.997329 167cc                       Options.enable_blob_files: false
2025/05/26-21:15:56.997336 167cc                           Options.min_blob_size: 0
2025/05/26-21:15:56.997342 167cc                          Options.blob_file_size: 268435456
2025/05/26-21:15:56.997359 167cc                   Options.blob_compression_type: NoCompression
2025/05/26-21:15:56.997367 167cc          Options.enable_blob_garbage_collection: false
2025/05/26-21:15:56.997374 167cc      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/05/26-21:15:56.997382 167cc Options.blob_garbage_collection_force_threshold: 1.000000
2025/05/26-21:15:57.000117 167cc [db/version_set.cc:4887] Recovered from manifest file:E:\CExperiment\ThreatRAG\test_milvus_lite\data\rocketmq/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/05/26-21:15:57.000140 167cc [db/version_set.cc:4902] Column family [default] (ID 0), log number is 0
2025/05/26-21:15:57.000309 167cc [db/version_set.cc:4385] Creating manifest 4
2025/05/26-21:15:57.004557 167cc [db/db_impl/db_impl_open.cc:1786] SstFileManager instance 0000019a6d813ae0
2025/05/26-21:15:57.004717 167cc DB pointer 0000019adcb21090
2025/05/26-21:16:00.012334 1510c [db/db_impl/db_impl.cc:1004] ------- DUMPING STATS -------
2025/05/26-21:16:00.012591 1510c [db/db_impl/db_impl.cc:1006] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x19a6d753300#93744 capacity: 1.90 GB collections: 1 last_copies: 1 last_secs: 4.4e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/05/26-21:19:25.509522 17238 [db/db_impl/db_impl.cc:472] Shutdown: canceling all background work
2025/05/26-21:19:25.511013 17238 [db/db_impl/db_impl.cc:685] Shutdown complete
