#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路径修复是否正确
"""

import os
import sys

# 添加packages目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'packages'))

def test_get_project_root():
    """测试获取项目根目录函数"""
    try:
        # 直接实现get_project_root逻辑，避免导入packages模块
        from pathlib import Path

        current_path = Path(__file__).resolve()
        root_indicators = ['.git', 'requirements.txt', 'pyproject.toml', 'setup.py', 'README.md']

        project_root = None
        for parent in current_path.parents:
            if any((parent / indicator).exists() for indicator in root_indicators):
                project_root = str(parent)
                break

        if project_root is None:
            project_root = str(current_path.parent)

        print(f"项目根目录: {project_root}")

        # 验证项目根目录是否正确
        expected_files = ['.git', 'README.md', 'packages']
        found_files = []

        for file in expected_files:
            file_path = os.path.join(project_root, file)
            if os.path.exists(file_path):
                found_files.append(file)
                print(f"✓ 找到: {file}")
            else:
                print(f"✗ 未找到: {file}")

        if len(found_files) >= 2:  # 至少找到2个标识文件
            print("✓ 项目根目录识别正确")
            return True
        else:
            print("✗ 项目根目录识别可能有问题")
            return False

    except Exception as e:
        print(f"✗ 测试get_project_root失败: {e}")
        return False

def test_config_paths():
    """测试配置文件路径"""
    try:
        # 直接实现路径逻辑，避免导入packages模块
        from pathlib import Path

        current_path = Path(__file__).resolve()
        root_indicators = ['.git', 'requirements.txt', 'pyproject.toml', 'setup.py', 'README.md']

        project_root = None
        for parent in current_path.parents:
            if any((parent / indicator).exists() for indicator in root_indicators):
                project_root = str(parent)
                break

        if project_root is None:
            project_root = str(current_path.parent)

        # 测试保存目录路径
        save_dir = os.path.join(project_root, "saves")
        print(f"配置保存目录: {save_dir}")

        # 检查static目录路径
        static_dir = os.path.join(project_root, "packages", "static")
        print(f"Static目录: {static_dir}")

        if os.path.exists(static_dir):
            print("✓ Static目录存在")

            # 检查models.yaml文件
            models_yaml = os.path.join(static_dir, "models.yaml")
            if os.path.exists(models_yaml):
                print("✓ models.yaml文件存在")
                return True
            else:
                print("✗ models.yaml文件不存在")
                return False
        else:
            print("✗ Static目录不存在")
            return False

    except Exception as e:
        print(f"✗ 测试配置路径失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("测试路径修复")
    print("=" * 50)

    tests = [
        ("项目根目录获取", test_get_project_root),
        ("配置文件路径", test_config_paths),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
            print(f"✓ {test_name} 通过")
        else:
            print(f"✗ {test_name} 失败")

    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
